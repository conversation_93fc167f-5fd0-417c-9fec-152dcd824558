# 导入椭圆曲线算法
from ecdsa import Signing<PERSON>ey, SECP256k1, Verifying<PERSON>ey, BadSignatureError
import binascii
import base64
from hashlib import sha256  # 用于哈希计算


class Wallet:
    """
    钱包类的定义
    """

    def __init__(self):
        """
        钱包初始化时基于椭圆曲线生成一个唯一的秘钥对，代表区块链上一个唯一的账户
        """
        self._private_key = SigningKey.generate(curve=SECP256k1)
        self._public_key = self._private_key.get_verifying_key()

    @property
    def address(self):  # TODO part 1
        """
        通过公钥生成地址
        """
        public_key_bytes = self._public_key.to_string()
        hash_object = sha256(public_key_bytes)
        return hash_object.hexdigest()

    @property
    def publicKey(self):
        """
        返回公钥字符串
        """
        return self._public_key.to_pem()

    def signature(self, message):  # TODO part 2
        """
        生成消息的数字签名
        """
        message_bytes = message.encode()
        return self._private_key.sign(message_bytes)


def verify_signature(publicKey, message, signature):  # TODO part 3
    """
    验证签名
    """
    try:
        vk = VerifyingKey.from_pem(publicKey)
        message_bytes = message.encode()
        return vk.verify(signature, message_bytes)
    except BadSignatureError:
        return False