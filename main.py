import random
from Logger import logger
from BlockChain import Blockchain
from Wallet import Wallet
from Network import Network, Peer

import tkinter
from tkinter import *
from tkinter import ttk  # 导入ttk模块，因为Combobox下拉菜单控件在ttk中
from tkinter import messagebox
import os
import atexit

# 全局变量
peers_registered = set()
peers_online = set()
peers_offline = []
peers_dict = {}  # 新增：保存 peerName 到 Peer 对象的映射

network = Network()
wallet = Wallet()
blockchain = Blockchain()
blockchain.create_genesis_block()

# 数据持久化文件名
BLOCKCHAIN_DATA_FILE = 'blockchain_data.json'

# 启动时自动加载区块链数据
if os.path.exists(BLOCKCHAIN_DATA_FILE):
    loaded = blockchain.load_from_file(BLOCKCHAIN_DATA_FILE)
    if loaded:
        print('区块链数据已加载')
        # 注意：这里不能立即调用update_ui_after_load()
        # 因为此时GUI元素还未创建
        # 我们将在GUI创建后调用
    else:
        print('区块链数据加载失败，使用新链')
else:
    print('未检测到区块链数据文件，使用新链')

# 退出时自动保存区块链数据
def save_blockchain_on_exit():
    blockchain.save_to_file(BLOCKCHAIN_DATA_FILE)
    print('区块链数据已保存')
atexit.register(save_blockchain_on_exit)

# 自定义颜色和字体
BG_COLOR = "#f0f0f0"
FG_COLOR = "#333333"
BUTTON_BG = "#4CAF50"
BUTTON_FG = "white"
FONT = ("Arial", 10)

def MenuCallback1():
    logger.info("系统初始化,生成随机节点...")
    blockchain_info_Text.insert(INSERT, "系统初始化,生成5个随机节点...\n")
    for _ in range(5):
        node = str(random.randint(1, 254)) + "." + str(random.randint(1, 254)) + "." + str(
            random.randint(1, 254)) + "." + str(random.randint(1, 254))
        logger.info(node)
        peers_online.add(node)
        peers_registered.add(node)
        blockchain_info_Text.insert(INSERT, node + "\n")

    # 刷新在线节点列表值
    online_peers_set = [i for i in peers_online]
    online_combobox['value'] = online_peers_set
    if online_peers_set:
        online_combobox.current(0)
    offline_peers_set = [i for i in peers_offline]
    offline_combobox['value'] = offline_peers_set
    if offline_peers_set:
        offline_combobox.current(0)
    register_peer_text.delete(0, 'end')

    # 交易地址控件赋值
    tx_address = [f"{name}:{peer.wallet.address}" for name, peer in peers_dict.items()]
    sender_peer['value'] = tx_address
    receiver_peer['value'] = tx_address
    sender_peer.current(0)
    receiver_peer.current(0)

def MenuCallback2():
    logger.info("退出系统...")
    if messagebox.askyesno("退出", "确定要退出系统吗？"):
        root.destroy()

def MenuCallback3():
    print("日志显示")

def peer_register():  # 节点注册
    peerName = register_peer_text.get()
    if not peerName:
        messagebox.showerror("错误", "请输入节点名称！")
        return
    if peerName in peers_dict:
        peer = peers_dict[peerName]
    else:
        peer = Peer(peerName)
        peers_dict[peerName] = peer

    peers_online.add(peerName)
    peers_registered.add(peerName)
    info1 = f"Peer {peerName} was Registered!"
    logger.info(info1)
    info2 = f"Current online peers: {peers_online}"
    logger.info(info2)
    blockchain_info_Text.insert(INSERT, info1 + "\n")
    blockchain_info_Text.insert(INSERT, info2 + "\n")

    # 刷新在线节点列表值
    online_peers_set = [i for i in peers_online]
    online_combobox['value'] = online_peers_set
    if online_peers_set:
        online_combobox.current(0)
    offline_peers_set = [i for i in peers_offline]
    offline_combobox['value'] = offline_peers_set
    if offline_peers_set:
        offline_combobox.current(0)
    register_peer_text.delete(0, 'end')

    # 交易地址控件赋值 - 所有已注册节点
    tx_address = [f"{name}:{peer.wallet.address}" for name, peer in peers_dict.items()]
    sender_peer['value'] = tx_address
    receiver_peer['value'] = tx_address
    print("tx_address:", tx_address)  # 调试输出

def peer_login():  # 节点登录
    peername = var_offline_peer.get()
    if not peername:
        messagebox.showerror("错误", "请选择离线节点！")
        return
    info = f"Peer {peername} Connected to the network."
    print(info)
    blockchain_info_Text.insert(INSERT, info + "\n")

    peers_online.add(peername)
    peers_offline.remove(peername)

    online_peers_set = [i for i in peers_online]
    online_combobox['value'] = online_peers_set
    if online_peers_set:
        online_combobox.current(0)
    offline_peers_set = [i for i in peers_offline]
    offline_combobox['value'] = offline_peers_set
    if offline_peers_set:
        offline_combobox.current(0)

def peer_logout():  # 节点注销
    peername = var_online_peer.get()
    if not peername:
        messagebox.showerror("错误", "请选择在线节点！")
        return
    info = f"Peer {peername} logged off."
    print(info)
    blockchain_info_Text.insert(INSERT, info + "\n")
    peers_offline.append(peername)
    peers_online.remove(peername)

    online_peers_set = [i for i in peers_online]
    online_combobox['value'] = online_peers_set
    if online_peers_set:
        online_combobox.current(0)
    offline_peers_set = [i for i in peers_offline]
    offline_combobox['value'] = offline_peers_set
    if offline_peers_set:
        offline_combobox.current(0)

def peer_send_tx():
    sender_name, sender_address = str(sender_peer.get()).split(":")
    receiver_name, receiver_address = str(receiver_peer.get()).split(":")
    data = tx_data.get()
    if not data:
        messagebox.showerror("错误", "请输入交易数据！")
        return
    blockchain.send_transaction(sender_address, receiver_address, data)

    info = f"Transaction was sent. From: {sender_name} To: {receiver_name}"
    blockchain_info_Text.insert(INSERT, info + "\n")

def print_unconfirmed_tx():
    blockchain_info_Text.insert(INSERT, "未确认交易数据:\n")
    if not blockchain.unconfirmed_transactions:
        blockchain_info_Text.insert(INSERT, "暂无未确认交易。\n")
    for i in blockchain.unconfirmed_transactions:
        blockchain_info_Text.insert(INSERT, str(i) + "\n")

def peer_tx_mine():
    num_tx_to_mine = len(blockchain.unconfirmed_transactions)
    if num_tx_to_mine == 0:
        messagebox.showinfo("提示", "暂无未确认交易，无需挖矿。")
        return
    ret = blockchain.mine()
    if ret:
        logger.info(f"{num_tx_to_mine} transactions were mined!")
        info = f"{num_tx_to_mine} transactions were mined!"
        info1 = "New Block added into the blockchain."
        blockchain_info_Text.insert(INSERT, info + "\n")
        blockchain_info_Text.insert(INSERT, info1 + "\n")
    else:
        messagebox.showerror("错误", "挖矿失败！")

def show_block_information():
    index = block_index_data.get()
    if index == "last":
        ret = blockchain.last_block
        if ret:
            logger.info(ret.__dict__)
            blockchain_info_Text.insert(INSERT, str(ret.__dict__) + "\n")
        else:
            messagebox.showerror("错误", "区块链为空！")
    else:
        try:
            index = int(index)
            if index >= blockchain.get_height():
                blockchain_info_Text.insert(INSERT, "Index exceed the height of the blockchain!\n")
                logger.info("Index exceed the height of the blockchain!")
                messagebox.showerror("错误", "索引超出区块链高度！")
                return
            else:
                ret = blockchain.get_block(index)
                if ret:
                    blockchain_info_Text.insert(INSERT, str(ret.__dict__) + "\n")
                    logger.info(ret.__dict__)
                else:
                    messagebox.showerror("错误", "未找到该区块！")
        except ValueError:
            messagebox.showerror("错误", "请输入有效的区块索引！")

def get_blockchain_height():
    info = f"当前区块链高度: {blockchain.get_height()}"
    logger.info(info)
    blockchain_info_Text.insert(INSERT, info + "\n")
    return blockchain.get_height()

def get_blockchain():
    peerName = online_combobox.get()
    if not peerName:
        messagebox.showerror("错误", "请选择在线节点！")
        return
    peer = peers_dict.get(peerName)
    if peer:
        peer.syn_blockchain(blockchain)
        logger.info(peer.print_blockchain())
    else:
        messagebox.showerror("错误", "未找到该节点！")

def show(event):
    online_peers_set = [i for i in peers_online]
    online_combobox['value'] = online_peers_set
    offline_peers_set = [i for i in peers_offline]
    offline_combobox['value'] = offline_peers_set

def reset_blockchain():
    if messagebox.askyesno("重置区块链", "确定要重置区块链吗？所有毕业证数据将被清空！"):
        global blockchain
        blockchain = Blockchain()
        blockchain.create_genesis_block()
        blockchain_info_Text.insert(INSERT, "区块链已重置，所有毕业证数据已清空！\n")

# 添加手动保存和加载按钮
def save_blockchain_manual():
    if blockchain.save_to_file(BLOCKCHAIN_DATA_FILE):
        messagebox.showinfo("成功", f"区块链数据已保存到 {BLOCKCHAIN_DATA_FILE}")
    else:
        messagebox.showerror("错误", "保存区块链数据失败")

def load_blockchain_manual():
    if blockchain.load_from_file(BLOCKCHAIN_DATA_FILE):
        messagebox.showinfo("成功", f"已从 {BLOCKCHAIN_DATA_FILE} 加载区块链数据")
        # 更新界面显示
        update_ui_after_load()
    else:
        messagebox.showerror("错误", "加载区块链数据失败")

def update_ui_after_load():
    """加载数据后更新界面显示"""
    # 清空当前显示
    blockchain_info_Text.delete(1.0, END)
    
    # 显示加载的区块链信息
    blockchain_info_Text.insert(INSERT, f"已加载区块链数据，共 {blockchain.get_height()} 个区块\n")
    blockchain_info_Text.insert(INSERT, "最新区块信息:\n")
    
    # 显示最新区块的信息
    last_block = blockchain.last_block
    if last_block:
        blockchain_info_Text.insert(INSERT, f"区块索引: {last_block.index}\n")
        blockchain_info_Text.insert(INSERT, f"时间戳: {last_block.timestamp}\n")
        blockchain_info_Text.insert(INSERT, f"区块哈希: {last_block.hash}\n")
        
        # 显示区块中的交易数量
        tx_count = len(last_block.transactions)
        blockchain_info_Text.insert(INSERT, f"包含 {tx_count} 笔交易/毕业证\n")
        
        # 如果有毕业证，显示最新的几个毕业证信息
        diploma_count = 0
        for tx in last_block.transactions:
            if isinstance(tx, dict) and tx.get('type') == 'diploma_issue':
                diploma_count += 1
                data = tx['data']
                blockchain_info_Text.insert(INSERT, f"\n毕业证ID: {data.get('diploma_id', '')[:10]}...\n")
                blockchain_info_Text.insert(INSERT, f"学生: {data.get('student_name', '')}, 专业: {data.get('major', '')}\n")
                # 只显示最新的3个毕业证
                if diploma_count >= 3:
                    blockchain_info_Text.insert(INSERT, "...\n")
                    break
    
    blockchain_info_Text.insert(INSERT, "\n使用区块链浏览器查看完整数据。\n")
    
    # 更新其他UI元素，如果有的话
    # 例如，更新下拉菜单等

root = Tk()
root.title("数字毕业证区块链系统")
root.configure(bg=BG_COLOR)

var_online_peer = tkinter.StringVar()
var_offline_peer = tkinter.StringVar()

# 菜单栏
menubar = Menu(root)
filemenu = Menu(menubar, tearoff=0)
filemenu.add_command(label="系统初始化", command=MenuCallback1)
filemenu.add_command(label="退出系统", command=MenuCallback2)
filemenu.add_separator()
filemenu.add_command(label="保存区块链数据", command=save_blockchain_manual)
filemenu.add_command(label="加载区块链数据", command=load_blockchain_manual)
menubar.add_cascade(label="文件", menu=filemenu)

logmenu = Menu(menubar, tearoff=0)
logmenu.add_command(label="日志显示", command=MenuCallback3)
menubar.add_cascade(label="日志", menu=logmenu)

root.config(menu=menubar)

function_op_grp = LabelFrame(root, text="功能区", relief=SUNKEN, borderwidth=2, bg=BG_COLOR, fg=FG_COLOR, font=FONT)
function_op_grp.grid(row=0, column=1, padx=10, pady=10)

peer_management_set = LabelFrame(function_op_grp, text="网络节点管理", padx=5, pady=5, relief=RAISED, bg=BG_COLOR, fg=FG_COLOR, font=FONT)
peer_management_set.grid(row=0, column=1, sticky=W, padx=5, pady=5)

online_label = Label(peer_management_set, text="在线节点", foreground=FG_COLOR, bg=BG_COLOR, font=FONT)
online_label.grid(row=1, column=1, sticky=W, padx=5, pady=5)
online_combobox = ttk.Combobox(peer_management_set, textvariable=var_online_peer, width=11, font=FONT)
online_combobox.bind('<<ComboboxSelected>>', show)

online_peers_set = [i for i in peers_online]
online_combobox['value'] = online_peers_set
online_combobox.grid(row=1, column=2, padx=5, pady=5)
logout_button = Button(peer_management_set, text="节点注销", command=peer_logout, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT)
logout_button.grid(row=1, column=3, padx=5, pady=5)

offline_label = Label(peer_management_set, text="离线节点", foreground=FG_COLOR, bg=BG_COLOR, font=FONT)
offline_label.grid(row=2, column=1, padx=5, pady=5)
offline_combobox = ttk.Combobox(peer_management_set, textvariable=var_offline_peer, width=11, font=FONT)

offline_peers_set = [i for i in peers_offline]
offline_combobox['value'] = offline_peers_set
offline_combobox.grid(row=2, column=2, padx=5, pady=5)
login_button = Button(peer_management_set, text="节点登录", command=peer_login, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT)
login_button.grid(row=2, column=3, padx=5, pady=5)

register_peer_text = Entry(peer_management_set, width=14, font=FONT)
register_peer_text.grid(row=3, column=2, sticky=W, padx=5, pady=5)
register_button = Button(peer_management_set, text="节点注册", command=peer_register, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT)
register_button.grid(row=3, column=3, sticky=E, padx=5, pady=5)

# 交易相关GUI
transaction_frame = LabelFrame(function_op_grp, text="交易管理", padx=5, pady=5, relief=RAISED, bg=BG_COLOR, fg=FG_COLOR, font=FONT)
transaction_frame.grid(row=1, column=1, sticky=W, padx=5, pady=5)

Label(transaction_frame, text="发送地址", foreground=FG_COLOR, bg=BG_COLOR, font=FONT).grid(row=1, column=1, sticky=W, padx=5, pady=5)
sender_peer = ttk.Combobox(transaction_frame, width=19, font=FONT)
sender_peer.grid(row=1, column=2, padx=5, pady=5)
Label(transaction_frame, text="接收地址", foreground=FG_COLOR, bg=BG_COLOR, font=FONT).grid(row=2, column=1, sticky=W, padx=5, pady=5)
receiver_peer = ttk.Combobox(transaction_frame, width=19, font=FONT)
receiver_peer.grid(row=2, column=2, padx=5, pady=5)
Label(transaction_frame, text="交易数据", foreground=FG_COLOR, bg=BG_COLOR, font=FONT).grid(row=3, column=1, sticky=W, padx=5, pady=5)
tx_data = Entry(transaction_frame, width=21, font=FONT)
tx_data.grid(row=3, column=2, padx=5, pady=5)
peer_send_tx = Button(transaction_frame, text="发送交易", command=peer_send_tx, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT)
peer_send_tx.grid(row=4, column=2, sticky=E, padx=5, pady=5)
Button(transaction_frame, text="数据上链", command=peer_tx_mine, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT).grid(row=5, column=1, sticky=W, padx=5, pady=5)
Button(transaction_frame, text="未确认交易池", command=print_unconfirmed_tx, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT).grid(row=5, column=2, sticky=E, padx=5, pady=5)

# 区块链相关操作
blockchain_op = LabelFrame(function_op_grp, text="区块与区块链", padx=5, pady=5, relief=RAISED, bg=BG_COLOR, fg=FG_COLOR, font=FONT)
blockchain_op.grid(row=2, column=1, sticky=W, padx=5, pady=5)
Label(blockchain_op, text="区块索引", foreground=FG_COLOR, bg=BG_COLOR, font=FONT).grid(row=0, column=1, sticky=W, padx=5, pady=5)
block_index_data = Entry(blockchain_op, width=10, font=FONT)
block_index_data.grid(row=0, column=2, sticky=W, padx=5, pady=5)
Button(blockchain_op, text="区块信息", command=show_block_information, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT).grid(row=0, column=3, sticky=E, padx=5, pady=5)
Button(blockchain_op, text="区块链高度", command=get_blockchain_height, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT).grid(row=1, column=1, sticky=W, padx=5, pady=5)
Button(blockchain_op, text="区块链信息", command=get_blockchain, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT).grid(row=1, column=3, sticky=W, padx=5, pady=5)
Button(blockchain_op, text="清空区块链", command=reset_blockchain, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT).grid(row=2, column=3, sticky=E, padx=5, pady=5)

# 毕业证管理相关GUI
diploma_frame = LabelFrame(function_op_grp, text="毕业证管理", padx=5, pady=5, relief=RAISED, bg=BG_COLOR, fg=FG_COLOR, font=FONT)
diploma_frame.grid(row=2, column=1, sticky=W, padx=5, pady=5)

# 颁发毕业证
issue_diploma_frame = LabelFrame(diploma_frame, text="颁发毕业证", padx=5, pady=5, bg=BG_COLOR, fg=FG_COLOR, font=FONT)
issue_diploma_frame.grid(row=0, column=0, sticky=W, padx=5, pady=5)

Label(issue_diploma_frame, text="学生ID:", foreground=FG_COLOR, bg=BG_COLOR, font=FONT).grid(row=0, column=0, sticky=W, padx=5, pady=5)
student_id_entry = Entry(issue_diploma_frame, width=20, font=FONT)
student_id_entry.grid(row=0, column=1, sticky=W, padx=5, pady=5)

Label(issue_diploma_frame, text="学生姓名:", foreground=FG_COLOR, bg=BG_COLOR, font=FONT).grid(row=1, column=0, sticky=W, padx=5, pady=5)
student_name_entry = Entry(issue_diploma_frame, width=20, font=FONT)
student_name_entry.grid(row=1, column=1, sticky=W, padx=5, pady=5)

Label(issue_diploma_frame, text="专业:", foreground=FG_COLOR, bg=BG_COLOR, font=FONT).grid(row=2, column=0, sticky=W, padx=5, pady=5)
major_entry = Entry(issue_diploma_frame, width=20, font=FONT)
major_entry.grid(row=2, column=1, sticky=W, padx=5, pady=5)

Label(issue_diploma_frame, text="毕业日期:", foreground=FG_COLOR, bg=BG_COLOR, font=FONT).grid(row=3, column=0, sticky=W, padx=5, pady=5)
graduation_date_entry = Entry(issue_diploma_frame, width=20, font=FONT)
graduation_date_entry.grid(row=3, column=1, sticky=W, padx=5, pady=5)

Label(issue_diploma_frame, text="GPA:", foreground=FG_COLOR, bg=BG_COLOR, font=FONT).grid(row=4, column=0, sticky=W, padx=5, pady=5)
gpa_entry = Entry(issue_diploma_frame, width=20, font=FONT)
gpa_entry.grid(row=4, column=1, sticky=W, padx=5, pady=5)

def issue_diploma():
    sender = sender_peer.get().split(":")[1]
    student_id = student_id_entry.get()
    student_name = student_name_entry.get()
    major = major_entry.get()
    graduation_date = graduation_date_entry.get()
    gpa = gpa_entry.get()

    if not all([student_id, student_name, major, graduation_date, gpa]):
        messagebox.showerror("错误", "请填写所有字段！")
        return

    try:
        gpa = float(gpa)
    except ValueError:
        messagebox.showerror("错误", "GPA必须是数字！")
        return

    transaction = blockchain.issue_diploma(sender, student_id, student_name, major, graduation_date, gpa)
    blockchain_info_Text.insert(INSERT, f"毕业证已颁发，交易ID: {transaction['data']['diploma_id']}\n")

    # 清空输入框
    student_id_entry.delete(0, 'end')
    student_name_entry.delete(0, 'end')
    major_entry.delete(0, 'end')
    graduation_date_entry.delete(0, 'end')
    gpa_entry.delete(0, 'end')

issue_button = Button(issue_diploma_frame, text="颁发毕业证", command=issue_diploma, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT)
issue_button.grid(row=5, column=0, columnspan=2, padx=5, pady=5)

# 验证毕业证
verify_diploma_frame = LabelFrame(diploma_frame, text="验证毕业证", padx=5, pady=5, bg=BG_COLOR, fg=FG_COLOR, font=FONT)
verify_diploma_frame.grid(row=0, column=1, sticky=W, padx=5, pady=5)

Label(verify_diploma_frame, text="毕业证ID:", foreground=FG_COLOR, bg=BG_COLOR, font=FONT).grid(row=0, column=0, sticky=W, padx=5, pady=5)
diploma_id_entry = Entry(verify_diploma_frame, width=40, font=FONT)
diploma_id_entry.grid(row=0, column=1, sticky=W, padx=5, pady=5)

def verify_diploma():
    diploma_id = diploma_id_entry.get()
    if not diploma_id:
        messagebox.showerror("错误", "请输入毕业证ID！")
        return

    is_valid, diploma_data = blockchain.verify_diploma(diploma_id)
    if is_valid:
        messagebox.showinfo("验证成功", f"学生ID: {diploma_data['student_id']}\n学生姓名: {diploma_data['student_name']}\n专业: {diploma_data['major']}\n毕业日期: {diploma_data['graduation_date']}\nGPA: {diploma_data['gpa']}\n颁发者: {diploma_data['issuer']}")
    else:
        messagebox.showerror("验证失败", "毕业证验证失败！")

    diploma_id_entry.delete(0, 'end')

verify_button = Button(verify_diploma_frame, text="验证毕业证", command=verify_diploma, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT)
verify_button.grid(row=1, column=0, columnspan=2, padx=5, pady=5)

# 查询学生毕业证
query_diploma_frame = LabelFrame(diploma_frame, text="查询学生毕业证", padx=5, pady=5, bg=BG_COLOR, fg=FG_COLOR, font=FONT)
query_diploma_frame.grid(row=0, column=2, sticky=W, padx=5, pady=5)

Label(query_diploma_frame, text="学生ID:", foreground=FG_COLOR, bg=BG_COLOR, font=FONT).grid(row=0, column=0, sticky=W, padx=5, pady=5)
query_student_id_entry = Entry(query_diploma_frame, width=20, font=FONT)
query_student_id_entry.grid(row=0, column=1, sticky=W, padx=5, pady=5)

def query_diplomas():
    student_id = query_student_id_entry.get()
    if not student_id:
        messagebox.showerror("错误", "请输入学生ID！")
        return

    diplomas = blockchain.get_student_diplomas(student_id)
    if diplomas:
        result = f"找到 {len(diplomas)} 个毕业证：\n"
        for diploma in diplomas:
            result += f"\n毕业证ID: {diploma['diploma_id']}\n学生姓名: {diploma['student_name']}\n专业: {diploma['major']}\n毕业日期: {diploma['graduation_date']}\nGPA: {diploma['gpa']}\n颁发者: {diploma['issuer']}\n"
        messagebox.showinfo("查询结果", result)
    else:
        messagebox.showinfo("查询结果", "未找到该学生的毕业证！")

    query_student_id_entry.delete(0, 'end')

query_button = Button(query_diploma_frame, text="查询毕业证", command=query_diplomas, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT)
query_button.grid(row=1, column=0, columnspan=2, padx=5, pady=5)

# 信息显示区域
display_info_grp = LabelFrame(root, width=800, text="综合信息显示区", bg=BG_COLOR, fg=FG_COLOR, font=FONT)
display_info_grp.grid(row=0, column=2, rowspan=3, padx=10, pady=10)
blockchain_info_Text = Text(display_info_grp, width=60, height=28, pady=5, font=FONT)
blockchain_info_Text.grid(row=0, column=1, padx=5, pady=5)

# 区块链浏览器功能
from tkinter.scrolledtext import ScrolledText

def show_blockchain_browser():
    win = Toplevel(root)
    win.title('区块链浏览器')
    win.geometry('800x600')
    txt = ScrolledText(win, width=100, height=35, font=FONT)
    txt.pack(fill=BOTH, expand=True)
    chain = blockchain.get_chain()
    for block in chain:
        txt.insert(END, f"区块索引: {block['index']}\n")
        txt.insert(END, f"时间戳: {block['timestamp']}\n")
        txt.insert(END, f"前一区块哈希: {block['previous_hash']}\n")
        txt.insert(END, f"本区块哈希: {block['hash']}\n")
        txt.insert(END, f"Nonce: {block['nonce']}\n")
        txt.insert(END, f"交易/毕业证信息:\n")
        for tx in block['transactions']:
            if isinstance(tx, dict) and 'type' in tx and tx['type'] == 'diploma_issue':
                # 毕业证交易
                data = tx['data']
                txt.insert(END, f"  毕业证ID: {data.get('diploma_id', '')}\n")
                txt.insert(END, f"  学生ID: {data.get('student_id', '')}\n")
                txt.insert(END, f"  姓名: {data.get('student_name', '')}\n")
                txt.insert(END, f"  专业: {data.get('major', '')}\n")
                txt.insert(END, f"  毕业日期: {data.get('graduation_date', '')}\n")
                txt.insert(END, f"  GPA: {data.get('gpa', '')}\n")
                txt.insert(END, f"  颁发者: {data.get('issuer', '')}\n")
                txt.insert(END, f"  颁发时间: {data.get('issue_date', '')}\n")
            else:
                txt.insert(END, f"  交易: {tx}\n")
        txt.insert(END, '-'*80 + '\n')
    txt.config(state=DISABLED)

# 在信息显示区上方加一个按钮
browser_btn = Button(display_info_grp, text="区块链浏览器", command=show_blockchain_browser, bg=BUTTON_BG, fg=BUTTON_FG, font=FONT)
browser_btn.grid(row=1, column=1, padx=5, pady=5, sticky=W)

# 如果启动时加载了数据，更新界面显示
if os.path.exists(BLOCKCHAIN_DATA_FILE) and blockchain.get_height() > 1:  # 高度>1表示不只有创世区块
    # 延迟100毫秒执行，确保GUI已完全加载
    root.after(100, update_ui_after_load)

mainloop()






