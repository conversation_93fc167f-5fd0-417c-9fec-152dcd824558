from hashlib import sha256
import time
import json
import ecdsa
import os

SECRET_KEY = 'SZTU'
CONNECTED_NODE_ADDRESS = "http://127.0.0.1:8000"


def create_accounts():
    accounts = []
    for i in range(10):
        seed = str(time.time())
        time.sleep(0.001)
        account = sha256(seed.encode()).hexdigest()
        accounts.append(account)
    return accounts


# Generate new Keys
privateKey = ecdsa.SigningKey.generate(curve=ecdsa.SECP256k1)
publicKey = privateKey.get_verifying_key()

message = "Testing message for validation."

# Generate Signature
signature = privateKey.sign(message.encode())


# To verify if the signature is valid
# print(Ecdsa.verify(message, signature, publicKey))

class Block:
    def __init__(self, index, transactions, timestamp, previous_hash, hash=0, nonce=0):
        self.index = index
        self.transactions = transactions
        self.timestamp = timestamp
        self.previous_hash = previous_hash
        self.nonce = nonce
        self.hash = hash

    def _calculate_hash(self):
        block_string = json.dumps(self.__dict__, sort_keys=True, indent=4)
        return sha256(block_string.encode()).hexdigest()


class Diploma:
    def __init__(self, student_id, student_name, major, graduation_date, gpa, issuer):
        self.student_id = student_id
        self.student_name = student_name
        self.major = major
        self.graduation_date = graduation_date
        self.gpa = gpa
        self.issuer = issuer
        self.issue_date = time.time()
        self.diploma_id = sha256(f"{student_id}{time.time()}".encode()).hexdigest()

    def to_dict(self):
        return {
            'student_id': self.student_id,
            'student_name': self.student_name,
            'major': self.major,
            'graduation_date': self.graduation_date,
            'gpa': self.gpa,
            'issuer': self.issuer,
            'issue_date': self.issue_date,
            'diploma_id': self.diploma_id
        }


class Blockchain:
    # difficulty of our PoW algorithm
    difficulty = 2

    def __init__(self):
        self.unconfirmed_transactions = []
        self.chain = []

    def create_genesis_block(self):
        '''
        A function to generate genesis block and appends it to
        the chain. The block has index 0, previous_hash as 0, and
        a valid hash.
        '''
        genesis_block = Block(0, [], 0, "0")
        genesis_block.hash = genesis_block._calculate_hash()
        self.chain.append(genesis_block)
        return genesis_block

    @property
    def last_block(self):  # TODO part 1
        # Returns the last block if the length is greater than zero
        if len(self.chain) > 0:
            return self.chain[-1]
        return None

    def get_height(self):  # TODO part 2
        return len(self.chain)

    def get_block(self, index):  # TODO part 3
        if 0 <= index < len(self.chain):
            return self.chain[index]
        return None

    def add_block(self, block, proof):  # TODO part 4
        """
        A function that adds the block to the chain after verification.
        Verification includes:
        * Checking if the proof is valid.
        * The previous_hash referred in the block and the hash of latest block
          in the chain match.
        """
        last_block = self.last_block
        if last_block is None:
            return False
        if self.is_valid_proof(block, proof) and block.previous_hash == last_block.hash:
            block.hash = proof
            self.chain.append(block)
            return True
        return False

    @staticmethod
    def proof_of_work(block):  # TODO part 5
        """
        Function that tries different values of nonce to get a hash
        that satisfies our difficulty criteria.
        """
        block.nonce = 0
        computed_hash = block._calculate_hash()
        while not computed_hash.startswith('0' * Blockchain.difficulty):
            block.nonce += 1
            computed_hash = block._calculate_hash()
        return computed_hash

    def add_new_transaction(self, transaction):  # TODO part 6
        self.unconfirmed_transactions.append(transaction)

    # Send Transaction
    def send_transaction(self, sender, receiver, data):  # TODO part 7
        transaction = {
            'sender': sender,
            'receiver': receiver,
            'data': data
        }
        self.add_new_transaction(transaction)
        return transaction

    # send transaction with private key
    def _send_transaction(self, sender, receiver, data, privateKey):  # TODO part 8
        transaction = {
            'sender': sender,
            'receiver': receiver,
            'data': data
        }
        message = json.dumps(transaction, sort_keys=True).encode()
        signature = privateKey.sign(message)
        transaction['signature'] = signature.hex()
        self.add_new_transaction(transaction)
        return transaction

    @classmethod
    def is_valid_proof(cls, block, block_hash):  # TODO part 9
        """
        Check if block_hash is valid hash of block and satisfies
        the difficulty criteria.
        """
        return block_hash.startswith('0' * cls.difficulty) and block_hash == block._calculate_hash()

    @classmethod
    def check_chain_validity(cls, chain):
        result = True
        previous_hash = "0"

        for block in chain:
            block_hash = block.hash
            # remove the hash field to recompute the hash again
            # using `_calculate_hash` method.
            delattr(block, "hash")

            if not cls.is_valid_proof(block, block_hash) or \
                    previous_hash != block.previous_hash:
                result = False
                break

            block.hash, previous_hash = block_hash, block_hash

        return result

    # 通过相邻区块previous_hash的一致性来检测区块链的完整性
    def check_block_validity(self, chain):  # TODO part 10
        result = True
        for i in range(1, len(chain)):
            current_block = chain[i]
            previous_block = chain[i - 1]
            if current_block.previous_hash != previous_block.hash:
                result = False
                break
        return result

    def mine(self):
        """
        This function serves as an interface to add the pending
        transactions to the blockchain by adding them to the block
        and figuring out Proof Of Work.
        """
        if not self.unconfirmed_transactions:
            return False

        last_block = self.last_block

        new_block = Block(index=last_block.index + 1,
                          transactions=self.unconfirmed_transactions,
                          timestamp=time.time(),
                          previous_hash=last_block.hash)

        proof = self.proof_of_work(new_block)
        self.add_block(new_block, proof)

        self.unconfirmed_transactions = []

        return True

    def _mine(self, publickey, signature, message):
        if not self.unconfirmed_transactions:
            return False

        if publickey.verify(signature, message.encode()):
            last_block = self.last_block

            new_block = Block(index=last_block.index + 1,
                              transactions=self.unconfirmed_transactions,
                              timestamp=time.time(),
                              previous_hash=last_block.hash)

            proof = self.proof_of_work(new_block)
            self.add_block(new_block, proof)

            self.unconfirmed_transactions = []

            return True
        else:
            return False

    def get_chain(self):
        chain_data = []
        for block in self.chain:
            chain_data.append(block.__dict__)

            # return json.dumps(chain_data,indent=4)
        return chain_data

    def issue_diploma(self, sender, student_id, student_name, major, graduation_date, gpa):
        """
        颁发毕业证
        """
        diploma = Diploma(student_id, student_name, major, graduation_date, gpa, sender)
        transaction = {
            'type': 'diploma_issue',
            'sender': sender,
            'data': diploma.to_dict()
        }
        self.add_new_transaction(transaction)
        return transaction

    def verify_diploma(self, diploma_id):
        """
        验证毕业证
        """
        for block in self.chain:
            for transaction in block.transactions:
                if transaction.get('type') == 'diploma_issue':
                    if transaction['data']['diploma_id'] == diploma_id:
                        return True, transaction['data']
        return False, None

    def get_student_diplomas(self, student_id):
        """
        获取学生的所有毕业证
        """
        diplomas = []
        for block in self.chain:
            for transaction in block.transactions:
                if transaction.get('type') == 'diploma_issue':
                    if transaction['data']['student_id'] == student_id:
                        diplomas.append(transaction['data'])
        return diplomas

    def save_to_file(self, filename):
        """
        将区块链数据保存到本地JSON文件
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.get_chain(), f, ensure_ascii=False, indent=4)
            print(f"区块链数据已保存到 {filename}")
            return True
        except Exception as e:
            print(f"保存区块链数据失败: {e}")
            return False

    def load_from_file(self, filename):
        """
        从本地JSON文件加载区块链数据
        """
        if not os.path.exists(filename):
            print(f"文件不存在: {filename}")
            return False
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                chain_data = json.load(f)
            self.chain = []
            for block_dict in chain_data:
                block = Block(
                    index=block_dict['index'],
                    transactions=block_dict['transactions'],
                    timestamp=block_dict['timestamp'],
                    previous_hash=block_dict['previous_hash'],
                    hash=block_dict['hash'],
                    nonce=block_dict['nonce']
                )
                self.chain.append(block)
            print(f"成功从 {filename} 加载了 {len(self.chain)} 个区块")
            return True
        except Exception as e:
            print(f"加载区块链数据失败: {e}")
            return False


def create_chain_from_dump(chain_dump):  # 从给定的JSON数据创建区块链
    generated_blockchain = Blockchain()
    generated_blockchain.create_genesis_block()
    for idx, block_data in enumerate(chain_dump):
        if idx == 0:
            continue  # skip genesis block
        block = Block(block_data["index"],
                      block_data["transactions"],
                      block_data["timestamp"],
                      block_data["previous_hash"],
                      block_data["nonce"])
        proof = block_data['hash']
        added = generated_blockchain.add_block(block, proof)
        if not added:
            raise Exception("The chain dump is tampered!!")
    return generated_blockchain
