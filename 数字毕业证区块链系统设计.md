# 数字毕业证区块链系统设计

## 1. 系统概要设计

### 1.1 系统总体架构

数字毕业证区块链系统采用分层架构设计，主要包括以下几个层次：

```
┌─────────────────────────────────────────┐
│           用户界面层 (GUI Layer)          │
│        基于Tkinter的图形用户界面          │
├─────────────────────────────────────────┤
│          业务逻辑层 (Logic Layer)         │
│    毕业证管理 | 交易处理 | 节点管理        │
├─────────────────────────────────────────┤
│         区块链核心层 (Core Layer)         │
│    区块链 | 区块 | 工作量证明 | 钱包       │
├─────────────────────────────────────────┤
│         网络通信层 (Network Layer)        │
│        节点管理 | P2P通信 | 数据同步       │
├─────────────────────────────────────────┤
│         数据持久化层 (Storage Layer)       │
│        JSON文件存储 | 数据序列化          │
└─────────────────────────────────────────┘
```

### 1.2 功能模块划分

#### 1.2.1 核心功能模块

**区块链管理模块 (BlockChain.py)**
- 区块链数据结构维护
- 工作量证明算法实现
- 交易验证与处理
- 毕业证颁发与验证

**钱包管理模块 (Wallet.py)**
- 椭圆曲线密钥对生成
- 数字签名生成与验证
- 地址生成算法

**网络管理模块 (Network.py)**
- 节点注册与管理
- 节点上线/下线控制
- 区块链数据同步

**日志管理模块 (Logger.py)**
- 系统运行日志记录
- 调试信息输出

#### 1.2.2 应用功能模块

**毕业证管理**
- 毕业证颁发功能
- 毕业证验证功能
- 学生毕业证查询功能

**交易管理**
- 交易创建与发送
- 未确认交易池管理
- 交易挖矿处理

**节点管理**
- 节点注册与登录
- 在线/离线状态管理
- 节点间通信

### 1.3 技术选型说明

| 技术组件 | 选型 | 说明 |
|---------|------|------|
| 编程语言 | Python 3.x | 简洁易读，丰富的第三方库支持 |
| 图形界面 | Tkinter | Python内置GUI库，轻量级 |
| 加密算法 | ECDSA (SECP256k1) | 椭圆曲线数字签名算法 |
| 哈希算法 | SHA-256 | 安全性高，广泛应用于区块链 |
| 数据存储 | JSON文件 | 轻量级，易于调试和维护 |
| 共识算法 | 工作量证明 (PoW) | 经典的区块链共识机制 |

### 1.4 系统工作流程

#### 1.4.1 毕业证颁发流程
```
用户输入毕业证信息 → 创建毕业证对象 → 生成交易 → 
添加到未确认交易池 → 挖矿打包 → 添加到区块链 → 
数据持久化保存
```

#### 1.4.2 毕业证验证流程
```
输入毕业证ID → 遍历区块链 → 查找匹配的毕业证交易 → 
验证数据完整性 → 返回验证结果
```

#### 1.4.3 节点管理流程
```
节点注册 → 生成钱包地址 → 加入在线节点列表 → 
同步区块链数据 → 参与交易和挖矿
```

## 2. 系统详细设计

### 2.1 核心类设计

#### 2.1.1 Block类设计

```python
class Block:
    def __init__(self, index, transactions, timestamp, previous_hash, hash=0, nonce=0):
        self.index = index              # 区块索引
        self.transactions = transactions # 交易列表
        self.timestamp = timestamp      # 时间戳
        self.previous_hash = previous_hash # 前一区块哈希
        self.nonce = nonce             # 工作量证明随机数
        self.hash = hash               # 当前区块哈希
```

**设计说明：**
- `index`: 区块在链中的位置，从0开始递增
- `transactions`: 存储该区块包含的所有交易
- `timestamp`: 区块创建时间，用于时序验证
- `previous_hash`: 链接前一个区块，保证链的完整性
- `nonce`: 挖矿过程中的随机数，用于工作量证明
- `hash`: 区块的唯一标识，通过SHA-256计算得出

#### 2.1.2 Blockchain类设计

```python
class Blockchain:
    difficulty = 2  # 挖矿难度
    
    def __init__(self):
        self.unconfirmed_transactions = []  # 未确认交易池
        self.chain = []                     # 区块链
```

**核心方法：**
- `create_genesis_block()`: 创建创世区块
- `add_block()`: 添加新区块到链中
- `proof_of_work()`: 工作量证明算法
- `mine()`: 挖矿处理未确认交易
- `issue_diploma()`: 颁发毕业证
- `verify_diploma()`: 验证毕业证
- `get_student_diplomas()`: 查询学生毕业证

#### 2.1.3 Diploma类设计

```python
class Diploma:
    def __init__(self, student_id, student_name, major, graduation_date, gpa, issuer):
        self.student_id = student_id           # 学生ID
        self.student_name = student_name       # 学生姓名
        self.major = major                     # 专业
        self.graduation_date = graduation_date # 毕业日期
        self.gpa = gpa                        # 学分绩点
        self.issuer = issuer                  # 颁发者
        self.issue_date = time.time()         # 颁发时间
        self.diploma_id = sha256(f"{student_id}{time.time()}".encode()).hexdigest()
```

**设计特点：**
- 包含完整的毕业证信息
- 自动生成唯一的毕业证ID
- 记录颁发时间戳
- 支持序列化为字典格式

#### 2.1.4 Wallet类设计

```python
class Wallet:
    def __init__(self):
        self._private_key = SigningKey.generate(curve=SECP256k1)
        self._public_key = self._private_key.get_verifying_key()
```

**核心功能：**
- 基于椭圆曲线生成密钥对
- 通过公钥生成唯一地址
- 提供数字签名功能
- 支持签名验证

### 2.2 数据结构设计

#### 2.2.1 交易数据结构

**普通交易结构：**
```json
{
    "sender": "发送者地址",
    "receiver": "接收者地址", 
    "data": "交易数据",
    "signature": "数字签名"
}
```

**毕业证交易结构：**
```json
{
    "type": "diploma_issue",
    "sender": "颁发者地址",
    "data": {
        "student_id": "学生ID",
        "student_name": "学生姓名",
        "major": "专业",
        "graduation_date": "毕业日期",
        "gpa": "学分绩点",
        "issuer": "颁发者",
        "issue_date": "颁发时间",
        "diploma_id": "毕业证唯一ID"
    }
}
```

#### 2.2.2 区块数据结构

```json
{
    "index": 1,
    "transactions": [交易列表],
    "timestamp": 1749644280.7123938,
    "previous_hash": "前一区块哈希值",
    "nonce": 50,
    "hash": "当前区块哈希值"
}
```

### 2.3 关键算法设计

#### 2.3.1 工作量证明算法

```python
@staticmethod
def proof_of_work(block):
    block.nonce = 0
    computed_hash = block._calculate_hash()
    while not computed_hash.startswith('0' * Blockchain.difficulty):
        block.nonce += 1
        computed_hash = block._calculate_hash()
    return computed_hash
```

**算法特点：**
- 基于SHA-256哈希计算
- 难度可调节（当前设置为2个前导零）
- 通过调整nonce值寻找满足条件的哈希
- 计算复杂度随难度指数增长

#### 2.3.2 区块链完整性验证算法

```python
@classmethod
def check_chain_validity(cls, chain):
    result = True
    previous_hash = "0"
    
    for block in chain:
        block_hash = block.hash
        delattr(block, "hash")
        
        if not cls.is_valid_proof(block, block_hash) or \
                previous_hash != block.previous_hash:
            result = False
            break
            
        block.hash, previous_hash = block_hash, block_hash
    
    return result
```

**验证要点：**
- 验证每个区块的工作量证明
- 检查区块间的哈希链接关系
- 确保区块链的完整性和一致性

#### 2.3.3 毕业证验证算法

```python
def verify_diploma(self, diploma_id):
    for block in self.chain:
        for transaction in block.transactions:
            if transaction.get('type') == 'diploma_issue':
                if transaction['data']['diploma_id'] == diploma_id:
                    return True, transaction['data']
    return False, None
```

**验证流程：**
- 遍历整个区块链
- 查找匹配的毕业证交易
- 返回验证结果和毕业证数据

### 2.4 用户界面设计

#### 2.4.1 主界面布局

系统采用Tkinter构建图形用户界面，主要包括以下区域：

```
┌─────────────────────────────────────────────────────────────┐
│                        菜单栏                                │
│  文件(系统初始化|退出|保存|加载)  日志(日志显示)              │
├─────────────────────────────────┬───────────────────────────┤
│            功能区                │                           │
│  ┌─────────────────────────────┐ │                           │
│  │       网络节点管理           │ │                           │
│  │  在线节点: [下拉框] [注销]   │ │                           │
│  │  离线节点: [下拉框] [登录]   │ │                           │
│  │  节点注册: [输入框] [注册]   │ │                           │
│  └─────────────────────────────┘ │                           │
│  ┌─────────────────────────────┐ │        综合信息显示区      │
│  │        交易管理             │ │                           │
│  │  发送地址: [下拉框]         │ │      [文本显示区域]        │
│  │  接收地址: [下拉框]         │ │                           │
│  │  交易数据: [输入框]         │ │                           │
│  │  [发送交易] [数据上链]      │ │                           │
│  │  [未确认交易池]             │ │                           │
│  └─────────────────────────────┘ │                           │
│  ┌─────────────────────────────┐ │                           │
│  │      区块与区块链           │ │                           │
│  │  区块索引: [输入] [查看]    │ │                           │
│  │  [区块链高度] [区块链信息]  │ │                           │
│  │  [清空区块链]               │ │                           │
│  └─────────────────────────────┘ │                           │
│  ┌─────────────────────────────┐ │                           │
│  │       毕业证管理            │ │                           │
│  │  颁发|验证|查询毕业证       │ │                           │
│  └─────────────────────────────┘ │                           │
└─────────────────────────────────┴───────────────────────────┘
```

#### 2.4.2 毕业证管理界面

**颁发毕业证界面：**
- 学生ID输入框
- 学生姓名输入框
- 专业输入框
- 毕业日期输入框
- GPA输入框
- 颁发按钮

**验证毕业证界面：**
- 毕业证ID输入框
- 验证按钮
- 结果显示弹窗

**查询毕业证界面：**
- 学生ID输入框
- 查询按钮
- 结果显示弹窗

#### 2.4.3 区块链浏览器

```python
def show_blockchain_browser():
    win = Toplevel(root)
    win.title('区块链浏览器')
    win.geometry('800x600')
    txt = ScrolledText(win, width=100, height=35)
    # 显示完整的区块链数据
```

**功能特点：**
- 独立窗口显示
- 滚动文本区域
- 完整区块信息展示
- 毕业证详细信息显示

### 2.5 数据持久化设计

#### 2.5.1 数据存储格式

系统采用JSON格式存储区块链数据，文件名为`blockchain_data.json`：

```json
[
    {
        "index": 0,
        "transactions": [],
        "timestamp": 0,
        "previous_hash": "0",
        "nonce": 0,
        "hash": "创世区块哈希"
    },
    {
        "index": 1,
        "transactions": [
            {
                "type": "diploma_issue",
                "sender": "颁发者地址",
                "data": {
                    "student_id": "2024001",
                    "student_name": "张三",
                    "major": "计算机科学与技术",
                    "graduation_date": "2024-06-30",
                    "gpa": 3.8,
                    "issuer": "颁发者地址",
                    "issue_date": 1749644278.9471571,
                    "diploma_id": "毕业证唯一ID"
                }
            }
        ],
        "timestamp": 1749644280.7123938,
        "previous_hash": "前一区块哈希",
        "nonce": 50,
        "hash": "当前区块哈希"
    }
]
```

#### 2.5.2 数据保存机制

**自动保存：**
```python
def save_blockchain_on_exit():
    blockchain.save_to_file(BLOCKCHAIN_DATA_FILE)
    print('区块链数据已保存')
atexit.register(save_blockchain_on_exit)
```

**手动保存：**
```python
def save_blockchain_manual():
    if blockchain.save_to_file(BLOCKCHAIN_DATA_FILE):
        messagebox.showinfo("成功", f"区块链数据已保存到 {BLOCKCHAIN_DATA_FILE}")
```

#### 2.5.3 数据加载机制

**启动时自动加载：**
```python
if os.path.exists(BLOCKCHAIN_DATA_FILE):
    loaded = blockchain.load_from_file(BLOCKCHAIN_DATA_FILE)
    if loaded:
        print('区块链数据已加载')
```

**手动加载：**
```python
def load_blockchain_manual():
    if blockchain.load_from_file(BLOCKCHAIN_DATA_FILE):
        messagebox.showinfo("成功", f"已从 {BLOCKCHAIN_DATA_FILE} 加载区块链数据")
        update_ui_after_load()
```

### 2.6 安全性设计

#### 2.6.1 密码学安全

**椭圆曲线数字签名：**
- 使用SECP256k1曲线
- 私钥长度256位
- 签名算法ECDSA
- 抗量子计算攻击

**哈希算法：**
- SHA-256哈希函数
- 256位输出长度
- 抗碰撞攻击
- 单向不可逆

#### 2.6.2 数据完整性

**区块链完整性：**
- 每个区块包含前一区块哈希
- 修改任意区块会破坏链式结构
- 工作量证明确保计算成本
- 全链验证机制

**毕业证防伪：**
- 唯一的毕业证ID
- 不可篡改的区块链存储
- 数字签名验证
- 时间戳证明

### 2.7 系统扩展性设计

#### 2.7.1 模块化设计

系统采用模块化设计，各模块职责清晰：
- 区块链核心模块独立
- 网络通信模块可扩展
- 用户界面模块可替换
- 存储模块可升级

#### 2.7.2 配置参数化

```python
# 可配置参数
BLOCKCHAIN_DATA_FILE = 'blockchain_data.json'  # 数据文件路径
difficulty = 2                                  # 挖矿难度
SECRET_KEY = 'SZTU'                            # 系统密钥
CONNECTED_NODE_ADDRESS = "http://127.0.0.1:8000"  # 节点地址
```

#### 2.7.3 功能扩展点

**支持的扩展功能：**
- 网络节点发现机制
- 分布式共识算法
- 智能合约支持
- 多种数字证书类型
- 权限管理系统
- API接口服务

## 3. 系统特色与创新

### 3.1 技术特色

1. **轻量级区块链实现**：采用Python实现的简洁区块链架构
2. **完整的毕业证生命周期**：从颁发到验证的全流程管理
3. **用户友好界面**：基于Tkinter的直观图形界面
4. **数据持久化**：支持区块链数据的保存和加载
5. **实时区块链浏览器**：可视化区块链数据展示

### 3.2 应用创新

1. **教育证书数字化**：将传统纸质毕业证转换为数字化形式
2. **防伪验证机制**：基于区块链的不可篡改特性
3. **去中心化存储**：分布式节点存储，提高数据安全性
4. **即时验证服务**：通过毕业证ID快速验证真伪
5. **学历信息查询**：支持按学生ID查询所有毕业证

### 3.3 实际应用价值

1. **提高证书可信度**：区块链技术确保毕业证真实性
2. **降低验证成本**：自动化验证替代人工核查
3. **防止学历造假**：技术手段杜绝虚假学历
4. **便于信息共享**：标准化的数字证书格式
5. **支持终身学习**：可记录多个学历证书

## 4. 总结

本数字毕业证区块链系统通过区块链技术实现了毕业证的数字化管理，具有以下优势：

1. **安全可靠**：基于密码学和区块链技术，确保数据安全
2. **操作简便**：图形化界面，用户操作简单直观
3. **功能完整**：涵盖毕业证颁发、验证、查询全流程
4. **扩展性强**：模块化设计，便于功能扩展
5. **实用性高**：解决了传统毕业证验证的痛点问题

该系统为教育行业的数字化转型提供了技术支撑，具有重要的理论意义和实用价值。
